<template>
  <div ref="custom-table" class="custom-table-container" :class="{ 'vab-fullscreen': isFullscreen }">
    <zcglLandInfoSearch :checkList="checkList" :columns="columns" :queryForm="searchForm" ref="zcglLandInfoTs" @handleAdd="handleAdd" @handleHeight="handleHeight" @handleSearch="handleSearch" @handleCheckedChange="handleCheckedChange" @handleExportRear="handleExportRear" @handleImportRear="handleImportRear" @handleExportTmpl="handleExportTmpl" @handleQuery="handleQuery" />

    <el-table ref="zcglLandInfoTable" v-loading="listLoading" border :data="list" :height="height" stripe @cell-dblclick="cellDblClick" id="ZcglLandInfo" row-key="zcliId" @sort-change="sortChange" highlight-current-row @current-change="currentSelectRow">

      <el-table-column align="center" label="序号" show-overflow-tooltip width="95" :index="count" type=index label-class-name="number" />

      <el-table-column v-for="(item, index) in finallyColumns" :key="index" align="center" :label="item.label" :prop="item.prop" :sortable="item.sortable" :width="item.width" show-overflow-tooltip :label-class-name="item.prop">
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" show-overflow-tooltip width="85" label-class-name="_lesoper">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
      </template>

    </el-table>

    <el-pagination background class="el-pagination-a" :current-page="searchForm.pageNo" :layout="layout" :page-size="searchForm.pageSize" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />

    <table-edit ref="zcglLandInfoEdit">
      <zcglLandInfoForm ref="zcglLandInfoForm" slot="form" :type="editType" :rules="rules" :form="form" :formConfig="formConfig" />
      <template slot="footerCont">
        <el-button @click="close">
          取 消
        </el-button>
        <el-button type="primary" @click="save" v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-edit>

    <table-search ref="zcglLandInfoQuerySearch">
      <zcglLandInfoQuery ref="zcglLandInfoQueryForm" slot="form" :form="queryForm" :formConfig="formConfig" />
      <template slot="footerCont">
        <el-button @click="queryClose">
          取 消
        </el-button>
        <el-button @click="queryClear">
          清 空
        </el-button>
        <el-button type="primary" @click="querySure" v-loading.fullscreen.lock="fullscreenLoading">
          确 定
        </el-button>
      </template>
    </table-search>

  </div>
</template>

<script>
import {
  zcglLandInfoDoDeleteELog,
  zcglLandInfoGetList,
  zcglLandInfoDoSaveOrUpdLog,
  zcglLandInfoDoExport
} from '@/api/zcglLandInfo'
import TableEdit from '@/views/common/TableEdit.vue'
import TableSearch from '@/views/common/TableSearch.vue'
import zcglLandInfoSearch from './components/Search.vue'
import zcglLandInfoForm from './components/Form.vue'
import zcglLandInfoQuery from './components/Query.vue'
import { exportRearEnd } from '@/api/exportExcel'
import { baseURL } from '@/config'
import { mapGetters } from 'vuex'

export default {
  name: 'zcglLandInfo',
  props: {
    gheight: {
      type: Number,
      default:500
    }
  },
  components: {
    TableEdit,
    TableSearch,
    zcglLandInfoSearch,
    zcglLandInfoForm,
    zcglLandInfoQuery
  },
  data () {
    return {
      fullscreenLoading: false,
      editType: '',
      queryFormDf: { amaiId: '', amaiIdEconomize: '', amaiIdMarket: '', cetcCode: '', companyId: '', companyName: '', companyTid: '', companyTname: '', networkFlag: '', zcliAddress: '', zcliAreaStart: undefined, zcliAreaEnd: undefined, zcliArea: '', zcliAssetsNo: '', zcliAsssetsmType: '', zcliBookValueStart: undefined, zcliBookValueEnd: undefined, zcliBookValue: '', zcliBusinessCyjcStart: undefined, zcliBusinessCyjcEnd: undefined, zcliBusinessCyjc: '', zcliBusinessDzzbStart: undefined, zcliBusinessDzzbEnd: undefined, zcliBusinessDzzb: '', zcliBusinessOtherStart: undefined, zcliBusinessOtherEnd: undefined, zcliBusinessOther: '', zcliBusinessWlaqStart: undefined, zcliBusinessWlaqEnd: undefined, zcliBusinessWlaq: '', zcliBusinessWxtxStart: undefined, zcliBusinessWxtxEnd: undefined, zcliBusinessWxtx: '', zcliCategory: '', zcliCertificateCode: '', zcliCheckOpinion: '', zcliCheckState: '', zcliCity: '', zcliCommitState: '', zcliCompanyLeader: '', zcliCompanyTel: '', zcliCorrespondingIncomeStart: undefined, zcliCorrespondingIncomeEnd: undefined, zcliCorrespondingIncome: '', zcliCountry: '', zcliCounty: '', zcliCreditCode: '', zcliDate: '', zcliDepartmentLeader: '', zcliDepartmentTel: '', zcliDepreciableYear: '', zcliDeptName: '', zcliEvaluateDate: '', zcliEvaluateValueStart: undefined, zcliEvaluateValueEnd: undefined, zcliEvaluateValue: '', zcliIdleStartTimeStart: undefined, zcliIdleStartTimeEnd: undefined, zcliIdleTimeStart: undefined, zcliIdleTimeEnd: undefined, zcliIdleTime: '', zcliIfAssets: '', zcliIfConstruction: '', zcliIfDispose: '', zcliIfDispute: '', zcliIfExist: '', zcliIfIssue: '', zcliIfMortgage: '', zcliIfStagnation: '', zcliLargeCategory: '', zcliLatitude: '', zcliLongitude: '', zcliMethods: '', zcliMj: '', zcliMortgageStart: undefined, zcliMortgageEnd: undefined, zcliMortgage: '', zcliNetbookValueStart: undefined, zcliNetbookValueEnd: undefined, zcliNetbookValue: '', zcliNocertificateReason: '', zcliOperator: '', zcliOperatorTel: '', zcliParkName: '', zcliPlotRatio: '', zcliProvince: '', zcliRange: '', zcliReasonDisposal: '', zcliReasonIdleness: '', zcliReasonStagnation: '', zcliRemark: '', zcliRentalIncomeLastyearStart: undefined, zcliRentalIncomeLastyearEnd: undefined, zcliRentalIncomeLastyear: '', zcliRentalIncomeThisyearStart: undefined, zcliRentalIncomeThisyearEnd: undefined, zcliRentalIncomeThisyear: '', zcliRequirements: '', zcliSerialNumberStart: undefined, zcliSerialNumberEnd: undefined, zcliSerialNumber: '', zcliServiceLife: '', zcliStateCzStart: undefined, zcliStateCzEnd: undefined, zcliStateCz: '', zcliStateXzStart: undefined, zcliStateXzEnd: undefined, zcliStateXz: '', zcliStateZyStart: undefined, zcliStateZyEnd: undefined, zcliStateZy: '', zcliStrategyDescribe: '', zcliSurroundingRentPriceStart: undefined, zcliSurroundingRentPriceEnd: undefined, zcliSurroundingRentPrice: '', zcliSurroundingSalePriceStart: undefined, zcliSurroundingSalePriceEnd: undefined, zcliSurroundingSalePrice: '', zcliTotalDepreciationStart: undefined, zcliTotalDepreciationEnd: undefined, zcliTotalDepreciation: '', zcliType: '', zcliUseDescribe: '', zcliUseful: '', zcliYear: '', zcpiName: '', zcxpId: '' },
      queryForm: { amaiId: '', amaiIdEconomize: '', amaiIdMarket: '', cetcCode: '', companyId: '', companyName: '', companyTid: '', companyTname: '', networkFlag: '', zcliAddress: '', zcliAreaStart: undefined, zcliAreaEnd: undefined, zcliArea: '', zcliAssetsNo: '', zcliAsssetsmType: '', zcliBookValueStart: undefined, zcliBookValueEnd: undefined, zcliBookValue: '', zcliBusinessCyjcStart: undefined, zcliBusinessCyjcEnd: undefined, zcliBusinessCyjc: '', zcliBusinessDzzbStart: undefined, zcliBusinessDzzbEnd: undefined, zcliBusinessDzzb: '', zcliBusinessOtherStart: undefined, zcliBusinessOtherEnd: undefined, zcliBusinessOther: '', zcliBusinessWlaqStart: undefined, zcliBusinessWlaqEnd: undefined, zcliBusinessWlaq: '', zcliBusinessWxtxStart: undefined, zcliBusinessWxtxEnd: undefined, zcliBusinessWxtx: '', zcliCategory: '', zcliCertificateCode: '', zcliCheckOpinion: '', zcliCheckState: '', zcliCity: '', zcliCommitState: '', zcliCompanyLeader: '', zcliCompanyTel: '', zcliCorrespondingIncomeStart: undefined, zcliCorrespondingIncomeEnd: undefined, zcliCorrespondingIncome: '', zcliCountry: '', zcliCounty: '', zcliCreditCode: '', zcliDate: '', zcliDepartmentLeader: '', zcliDepartmentTel: '', zcliDepreciableYear: '', zcliDeptName: '', zcliEvaluateDate: '', zcliEvaluateValueStart: undefined, zcliEvaluateValueEnd: undefined, zcliEvaluateValue: '', zcliIdleStartTimeStart: undefined, zcliIdleStartTimeEnd: undefined, zcliIdleTimeStart: undefined, zcliIdleTimeEnd: undefined, zcliIdleTime: '', zcliIfAssets: '', zcliIfConstruction: '', zcliIfDispose: '', zcliIfDispute: '', zcliIfExist: '', zcliIfIssue: '', zcliIfMortgage: '', zcliIfStagnation: '', zcliLargeCategory: '', zcliLatitude: '', zcliLongitude: '', zcliMethods: '', zcliMj: '', zcliMortgageStart: undefined, zcliMortgageEnd: undefined, zcliMortgage: '', zcliNetbookValueStart: undefined, zcliNetbookValueEnd: undefined, zcliNetbookValue: '', zcliNocertificateReason: '', zcliOperator: '', zcliOperatorTel: '', zcliParkName: '', zcliPlotRatio: '', zcliProvince: '', zcliRange: '', zcliReasonDisposal: '', zcliReasonIdleness: '', zcliReasonStagnation: '', zcliRemark: '', zcliRentalIncomeLastyearStart: undefined, zcliRentalIncomeLastyearEnd: undefined, zcliRentalIncomeLastyear: '', zcliRentalIncomeThisyearStart: undefined, zcliRentalIncomeThisyearEnd: undefined, zcliRentalIncomeThisyear: '', zcliRequirements: '', zcliSerialNumberStart: undefined, zcliSerialNumberEnd: undefined, zcliSerialNumber: '', zcliServiceLife: '', zcliStateCzStart: undefined, zcliStateCzEnd: undefined, zcliStateCz: '', zcliStateXzStart: undefined, zcliStateXzEnd: undefined, zcliStateXz: '', zcliStateZyStart: undefined, zcliStateZyEnd: undefined, zcliStateZy: '', zcliStrategyDescribe: '', zcliSurroundingRentPriceStart: undefined, zcliSurroundingRentPriceEnd: undefined, zcliSurroundingRentPrice: '', zcliSurroundingSalePriceStart: undefined, zcliSurroundingSalePriceEnd: undefined, zcliSurroundingSalePrice: '', zcliTotalDepreciationStart: undefined, zcliTotalDepreciationEnd: undefined, zcliTotalDepreciation: '', zcliType: '', zcliUseDescribe: '', zcliUseful: '', zcliYear: '', zcpiName: '', zcxpId: '' },
      form: { amaiId: '', amaiIdEconomize: '', amaiIdMarket: '', cetcCode: '', companyId: '', companyName: '', companyTid: '', companyTname: '', networkFlag: '', zcliAddress: '', zcliArea: '', zcliAssetsNo: '', zcliAsssetsmType: '', zcliBookValue: '', zcliBusinessCyjc: '', zcliBusinessDzzb: '', zcliBusinessOther: '', zcliBusinessWlaq: '', zcliBusinessWxtx: '', zcliCategory: '', zcliCertificateCode: '', zcliCheckOpinion: '', zcliCheckState: '', zcliCity: '', zcliCommitState: '', zcliCompanyLeader: '', zcliCompanyTel: '', zcliCorrespondingIncome: '', zcliCountry: '', zcliCounty: '', zcliCreditCode: '', zcliDate: '', zcliDepartmentLeader: '', zcliDepartmentTel: '', zcliDepreciableYear: '', zcliDeptName: '', zcliEvaluateDate: '', zcliEvaluateValue: '', zcliIdleStartTime: '', zcliIdleTime: '', zcliIfAssets: '', zcliIfConstruction: '', zcliIfDispose: '', zcliIfDispute: '', zcliIfExist: '', zcliIfIssue: '', zcliIfMortgage: '', zcliIfStagnation: '', zcliLargeCategory: '', zcliLatitude: '', zcliLongitude: '', zcliMethods: '', zcliMj: '', zcliMortgage: '', zcliNetbookValue: '', zcliNocertificateReason: '', zcliOperator: '', zcliOperatorTel: '', zcliParkName: '', zcliPlotRatio: '', zcliProvince: '', zcliRange: '', zcliReasonDisposal: '', zcliReasonIdleness: '', zcliReasonStagnation: '', zcliRemark: '', zcliRentalIncomeLastyear: '', zcliRentalIncomeThisyear: '', zcliRequirements: '', zcliSerialNumber: '', zcliServiceLife: '', zcliStateCz: '', zcliStateXz: '', zcliStateZy: '', zcliStrategyDescribe: '', zcliSurroundingRentPrice: '', zcliSurroundingSalePrice: '', zcliTotalDepreciation: '', zcliType: '', zcliUseDescribe: '', zcliUseful: '', zcliYear: '', zcpiName: '', zcxpId: '' },
      rules: {
        amaiId: [
          { required: true, message: '请输入坐落位置（区）', trigger: 'blur' }
        ],
        amaiIdEconomize: [
          { required: true, message: '请输入坐落位置---省', trigger: 'blur' }
        ],
        amaiIdMarket: [
          { required: true, message: '请输入坐落位置-市', trigger: 'blur' }
        ],
        cetcCode: [
          { required: true, message: '请输入集团编码', trigger: 'blur' }
        ],
        companyId: [
          { required: true, message: '请输入本单位，关联ZCGL_COMPANY_INFO', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入本单位名称', trigger: 'blur' }
        ],
        companyTid: [
          { required: true, message: '请输入二级成员单位', trigger: 'blur' }
        ],
        companyTname: [
          { required: true, message: '请输入二级单位名称', trigger: 'blur' }
        ],
        networkFlag: [
          { required: true, message: '请输入数据来源', trigger: 'blur' }
        ],
        zcliAddress: [
          { required: true, message: '请输入具体土地位置', trigger: 'blur' }
        ],
        zcliArea: [
          { required: true, message: '请输入土地总面积', trigger: 'blur' }
        ],
        zcliAssetsNo: [
          { required: true, message: '请输入资产编号', trigger: 'blur' }
        ],
        zcliAsssetsmType: [
          { required: true, message: '请输入资产类型（事业单位资产、企业单位资产）', trigger: 'blur' }
        ],
        zcliBookValue: [
          { required: true, message: '请输入原值', trigger: 'blur' }
        ],
        zcliBusinessCyjc: [
          { required: true, message: '请输入产业基础面积', trigger: 'blur' }
        ],
        zcliBusinessDzzb: [
          { required: true, message: '请输入电子装备面积', trigger: 'blur' }
        ],
        zcliBusinessOther: [
          { required: true, message: '请输入其它面积', trigger: 'blur' }
        ],
        zcliBusinessWlaq: [
          { required: true, message: '请输入网络安全面积', trigger: 'blur' }
        ],
        zcliBusinessWxtx: [
          { required: true, message: '请输入网信体系面积', trigger: 'blur' }
        ],
        zcliCategory: [
          { required: true, message: '请输入门类', trigger: 'blur' }
        ],
        zcliCertificateCode: [
          { required: true, message: '请输入土地权属证明编号', trigger: 'blur' }
        ],
        zcliCheckOpinion: [
          { required: true, message: '请输入审核意见', trigger: 'blur' }
        ],
        zcliCheckState: [
          { required: true, message: '请输入审核状态', trigger: 'blur' }
        ],
        zcliCity: [
          { required: true, message: '请输入土地所在市', trigger: 'blur' }
        ],
        zcliCommitState: [
          { required: true, message: '请输入提交状态', trigger: 'blur' }
        ],
        zcliCompanyLeader: [
          { required: true, message: '请输入分管所(公司)领导', trigger: 'blur' }
        ],
        zcliCompanyTel: [
          { required: true, message: '请输入分管所(公司)领导联系方式', trigger: 'blur' }
        ],
        zcliCorrespondingIncome: [
          { required: true, message: '请输入土地面积对应本年营业收入（万元）', trigger: 'blur' }
        ],
        zcliCountry: [
          { required: true, message: '请输入国家或地区', trigger: 'blur' }
        ],
        zcliCounty: [
          { required: true, message: '请输入土地所在区县', trigger: 'blur' }
        ],
        zcliCreditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ],
        zcliDate: [
          { required: true, message: '请输入登记时间', trigger: 'blur' }
        ],
        zcliDepartmentLeader: [
          { required: true, message: '请输入部门负责人', trigger: 'blur' }
        ],
        zcliDepartmentTel: [
          { required: true, message: '请输入部门负责人联系方式', trigger: 'blur' }
        ],
        zcliDepreciableYear: [
          { required: true, message: '请输入折旧年限', trigger: 'blur' }
        ],
        zcliDeptName: [
          { required: true, message: '请输入业务主管部门名称', trigger: 'blur' }
        ],
        zcliEvaluateDate: [
          { required: true, message: '请输入最近评估日期', trigger: 'blur' }
        ],
        zcliEvaluateValue: [
          { required: true, message: '请输入最近评估价值（万元）', trigger: 'blur' }
        ],
        zcliIdleStartTime: [
          { required: true, message: '请输入闲置起始时间', trigger: 'blur' }
        ],
        zcliIdleTime: [
          { required: true, message: '请输入空置时间', trigger: 'blur' }
        ],
        zcliIfAssets: [
          { required: true, message: '请输入是否两非资产', trigger: 'blur' }
        ],
        zcliIfConstruction: [
          { required: true, message: '请输入是否在建', trigger: 'blur' }
        ],
        zcliIfDispose: [
          { required: true, message: '请输入是否可处置', trigger: 'blur' }
        ],
        zcliIfDispute: [
          { required: true, message: '请输入是否存在纠纷', trigger: 'blur' }
        ],
        zcliIfExist: [
          { required: true, message: '请输入是否取得土地权属证明编号', trigger: 'blur' }
        ],
        zcliIfIssue: [
          { required: true, message: '请输入是否下达', trigger: 'blur' }
        ],
        zcliIfMortgage: [
          { required: true, message: '请输入土地是否已抵押', trigger: 'blur' }
        ],
        zcliIfStagnation: [
          { required: true, message: '请输入是否延期或停滞', trigger: 'blur' }
        ],
        zcliLargeCategory: [
          { required: true, message: '请输入大类', trigger: 'blur' }
        ],
        zcliLatitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' }
        ],
        zcliLongitude: [
          { required: true, message: '请输入经度', trigger: 'blur' }
        ],
        zcliMethods: [
          { required: true, message: '请输入建议盘活/处置方式', trigger: 'blur' }
        ],
        zcliMj: [
          { required: true, message: '请输入密级', trigger: 'blur' }
        ],
        zcliMortgage: [
          { required: true, message: '请输入其中：已抵押面积', trigger: 'blur' }
        ],
        zcliNetbookValue: [
          { required: true, message: '请输入净值', trigger: 'blur' }
        ],
        zcliNocertificateReason: [
          { required: true, message: '请输入其中：未取得土地证明原因', trigger: 'blur' }
        ],
        zcliOperator: [
          { required: true, message: '请输入经办人', trigger: 'blur' }
        ],
        zcliOperatorTel: [
          { required: true, message: '请输入经办人联系方式', trigger: 'blur' }
        ],
        zcliParkName: [
          { required: true, message: '请输入本地块所属园区', trigger: 'blur' }
        ],
        zcliPlotRatio: [
          { required: true, message: '请输入容积率', trigger: 'blur' }
        ],
        zcliProvince: [
          { required: true, message: '请输入土地所在省（直辖市、自治区）', trigger: 'blur' }
        ],
        zcliRange: [
          { required: true, message: '请输入境内境外', trigger: 'blur' }
        ],
        zcliReasonDisposal: [
          { required: true, message: '请输入不可处置原因', trigger: 'blur' }
        ],
        zcliReasonIdleness: [
          { required: true, message: '请输入闲置原因', trigger: 'blur' }
        ],
        zcliReasonStagnation: [
          { required: true, message: '请输入延期/停滞原因', trigger: 'blur' }
        ],
        zcliRemark: [
          { required: true, message: '请输入备注', trigger: 'blur' }
        ],
        zcliRentalIncomeLastyear: [
          { required: true, message: '请输入上年租金收入（万元）', trigger: 'blur' }
        ],
        zcliRentalIncomeThisyear: [
          { required: true, message: '请输入预计本年租金收入（万元）', trigger: 'blur' }
        ],
        zcliRequirements: [
          { required: true, message: '请输入地上建筑是否满族批复或者备案功能要求', trigger: 'blur' }
        ],
        zcliSerialNumber: [
          { required: true, message: '请输入序号', trigger: 'blur' }
        ],
        zcliServiceLife: [
          { required: true, message: '请输入土地使用年限(年)', trigger: 'blur' }
        ],
        zcliStateCz: [
          { required: true, message: '请输入出租面积', trigger: 'blur' }
        ],
        zcliStateXz: [
          { required: true, message: '请输入闲置面积', trigger: 'blur' }
        ],
        zcliStateZy: [
          { required: true, message: '请输入自用面积', trigger: 'blur' }
        ],
        zcliStrategyDescribe: [
          { required: true, message: '请输入是否有战略安排', trigger: 'blur' }
        ],
        zcliSurroundingRentPrice: [
          { required: true, message: '请输入上年周边可比土地出租单价（元/平方米/月）', trigger: 'blur' }
        ],
        zcliSurroundingSalePrice: [
          { required: true, message: '请输入上年周边可比土地出售单价（元/平方米/月）', trigger: 'blur' }
        ],
        zcliTotalDepreciation: [
          { required: true, message: '请输入本年计提折旧总额（万元）', trigger: 'blur' }
        ],
        zcliType: [
          { required: true, message: '请输入取得方式', trigger: 'blur' }
        ],
        zcliUseDescribe: [
          { required: true, message: '请输入现状用途描述', trigger: 'blur' }
        ],
        zcliUseful: [
          { required: true, message: '请输入土地性质', trigger: 'blur' }
        ],
        zcliYear: [
          { required: true, message: '请输入年份', trigger: 'blur' }
        ],
        zcpiName: [
          { required: true, message: '请输入批复项目名称', trigger: 'blur' }
        ],
        zcxpId: [
          { required: true, message: '请输入关联项目类固定资产主键ID', trigger: 'blur' }
        ]
      },
      formConfig: { labelPosition: 'right', labelWidth: '80px', size: 'small' },
      title: '',
      isFullscreen: false,
      height: !this.gheight ? this.$baseTableHeight(1, 1) : this.gheight,
      checkList: ['坐落位置（区）', '坐落位置---省', '坐落位置-市', '集团编码', '本单位，关联ZCGL_COMPANY_INFO', '本单位名称', '二级成员单位', '二级单位名称', '数据来源', '具体土地位置', '土地总面积', '资产编号', '资产类型（事业单位资产、企业单位资产）', '原值', '产业基础面积', '电子装备面积', '其它面积', '网络安全面积', '网信体系面积', '门类', '土地权属证明编号', '审核意见', '审核状态', '土地所在市', '提交状态', '分管所(公司)领导', '分管所(公司)领导联系方式', '土地面积对应本年营业收入（万元）', '国家或地区', '土地所在区县', '统一社会信用代码', '登记时间', '部门负责人', '部门负责人联系方式', '折旧年限', '业务主管部门名称', '最近评估日期', '最近评估价值（万元）', '闲置起始时间', '空置时间', '是否两非资产', '是否在建', '是否可处置', '是否存在纠纷', '是否取得土地权属证明编号', '是否下达', '土地是否已抵押', '是否延期或停滞', '大类', '纬度', '经度', '建议盘活/处置方式', '密级', '其中：已抵押面积', '净值', '其中：未取得土地证明原因', '经办人', '经办人联系方式', '本地块所属园区', '容积率', '土地所在省（直辖市、自治区）', '境内境外', '不可处置原因', '闲置原因', '延期/停滞原因', '备注', '上年租金收入（万元）', '预计本年租金收入（万元）', '地上建筑是否满族批复或者备案功能要求', '序号', '土地使用年限(年)', '出租面积', '闲置面积', '自用面积', '是否有战略安排', '上年周边可比土地出租单价（元/平方米/月）', '上年周边可比土地出售单价（元/平方米/月）', '本年计提折旧总额（万元）', '取得方式', '现状用途描述', '土地性质', '年份', '批复项目名称', '关联项目类固定资产主键ID'],
      columns: [
        { prop: 'amaiId', label: '坐落位置（区）', width: 'auto', sortable: true },
        { prop: 'amaiIdEconomize', label: '坐落位置---省', width: 'auto', sortable: true },
        { prop: 'amaiIdMarket', label: '坐落位置-市', width: 'auto', sortable: true },
        { prop: 'cetcCode', label: '集团编码', width: 'auto', sortable: true },
        { prop: 'companyId', label: '本单位，关联ZCGL_COMPANY_INFO', width: 'auto', sortable: true },
        { prop: 'companyName', label: '本单位名称', width: 'auto', sortable: true },
        { prop: 'companyTid', label: '二级成员单位', width: 'auto', sortable: true },
        { prop: 'companyTname', label: '二级单位名称', width: 'auto', sortable: true },
        { prop: 'networkFlag', label: '数据来源', width: 'auto', sortable: true },
        { prop: 'zcliAddress', label: '具体土地位置', width: 'auto', sortable: true },
        { prop: 'zcliArea', label: '土地总面积', width: 'auto', sortable: true },
        { prop: 'zcliAssetsNo', label: '资产编号', width: 'auto', sortable: true },
        { prop: 'zcliAsssetsmType', label: '资产类型（事业单位资产、企业单位资产）', width: 'auto', sortable: true },
        { prop: 'zcliBookValue', label: '原值', width: 'auto', sortable: true },
        { prop: 'zcliBusinessCyjc', label: '产业基础面积', width: 'auto', sortable: true },
        { prop: 'zcliBusinessDzzb', label: '电子装备面积', width: 'auto', sortable: true },
        { prop: 'zcliBusinessOther', label: '其它面积', width: 'auto', sortable: true },
        { prop: 'zcliBusinessWlaq', label: '网络安全面积', width: 'auto', sortable: true },
        { prop: 'zcliBusinessWxtx', label: '网信体系面积', width: 'auto', sortable: true },
        { prop: 'zcliCategory', label: '门类', width: 'auto', sortable: true },
        { prop: 'zcliCertificateCode', label: '土地权属证明编号', width: 'auto', sortable: true },
        { prop: 'zcliCheckOpinion', label: '审核意见', width: 'auto', sortable: true },
        { prop: 'zcliCheckState', label: '审核状态', width: 'auto', sortable: true },
        { prop: 'zcliCity', label: '土地所在市', width: 'auto', sortable: true },
        { prop: 'zcliCommitState', label: '提交状态', width: 'auto', sortable: true },
        { prop: 'zcliCompanyLeader', label: '分管所(公司)领导', width: 'auto', sortable: true },
        { prop: 'zcliCompanyTel', label: '分管所(公司)领导联系方式', width: 'auto', sortable: true },
        { prop: 'zcliCorrespondingIncome', label: '土地面积对应本年营业收入（万元）', width: 'auto', sortable: true },
        { prop: 'zcliCountry', label: '国家或地区', width: 'auto', sortable: true },
        { prop: 'zcliCounty', label: '土地所在区县', width: 'auto', sortable: true },
        { prop: 'zcliCreditCode', label: '统一社会信用代码', width: 'auto', sortable: true },
        { prop: 'zcliDate', label: '登记时间', width: 'auto', sortable: true },
        { prop: 'zcliDepartmentLeader', label: '部门负责人', width: 'auto', sortable: true },
        { prop: 'zcliDepartmentTel', label: '部门负责人联系方式', width: 'auto', sortable: true },
        { prop: 'zcliDepreciableYear', label: '折旧年限', width: 'auto', sortable: true },
        { prop: 'zcliDeptName', label: '业务主管部门名称', width: 'auto', sortable: true },
        { prop: 'zcliEvaluateDate', label: '最近评估日期', width: 'auto', sortable: true },
        { prop: 'zcliEvaluateValue', label: '最近评估价值（万元）', width: 'auto', sortable: true },
        { prop: 'zcliIdleStartTime', label: '闲置起始时间', width: 'auto', sortable: true },
        { prop: 'zcliIdleTime', label: '空置时间', width: 'auto', sortable: true },
        { prop: 'zcliIfAssets', label: '是否两非资产', width: 'auto', sortable: true },
        { prop: 'zcliIfConstruction', label: '是否在建', width: 'auto', sortable: true },
        { prop: 'zcliIfDispose', label: '是否可处置', width: 'auto', sortable: true },
        { prop: 'zcliIfDispute', label: '是否存在纠纷', width: 'auto', sortable: true },
        { prop: 'zcliIfExist', label: '是否取得土地权属证明编号', width: 'auto', sortable: true },
        { prop: 'zcliIfIssue', label: '是否下达', width: 'auto', sortable: true },
        { prop: 'zcliIfMortgage', label: '土地是否已抵押', width: 'auto', sortable: true },
        { prop: 'zcliIfStagnation', label: '是否延期或停滞', width: 'auto', sortable: true },
        { prop: 'zcliLargeCategory', label: '大类', width: 'auto', sortable: true },
        { prop: 'zcliLatitude', label: '纬度', width: 'auto', sortable: true },
        { prop: 'zcliLongitude', label: '经度', width: 'auto', sortable: true },
        { prop: 'zcliMethods', label: '建议盘活/处置方式', width: 'auto', sortable: true },
        { prop: 'zcliMj', label: '密级', width: 'auto', sortable: true },
        { prop: 'zcliMortgage', label: '其中：已抵押面积', width: 'auto', sortable: true },
        { prop: 'zcliNetbookValue', label: '净值', width: 'auto', sortable: true },
        { prop: 'zcliNocertificateReason', label: '其中：未取得土地证明原因', width: 'auto', sortable: true },
        { prop: 'zcliOperator', label: '经办人', width: 'auto', sortable: true },
        { prop: 'zcliOperatorTel', label: '经办人联系方式', width: 'auto', sortable: true },
        { prop: 'zcliParkName', label: '本地块所属园区', width: 'auto', sortable: true },
        { prop: 'zcliPlotRatio', label: '容积率', width: 'auto', sortable: true },
        { prop: 'zcliProvince', label: '土地所在省（直辖市、自治区）', width: 'auto', sortable: true },
        { prop: 'zcliRange', label: '境内境外', width: 'auto', sortable: true },
        { prop: 'zcliReasonDisposal', label: '不可处置原因', width: 'auto', sortable: true },
        { prop: 'zcliReasonIdleness', label: '闲置原因', width: 'auto', sortable: true },
        { prop: 'zcliReasonStagnation', label: '延期/停滞原因', width: 'auto', sortable: true },
        { prop: 'zcliRemark', label: '备注', width: 'auto', sortable: true },
        { prop: 'zcliRentalIncomeLastyear', label: '上年租金收入（万元）', width: 'auto', sortable: true },
        { prop: 'zcliRentalIncomeThisyear', label: '预计本年租金收入（万元）', width: 'auto', sortable: true },
        { prop: 'zcliRequirements', label: '地上建筑是否满族批复或者备案功能要求', width: 'auto', sortable: true },
        { prop: 'zcliSerialNumber', label: '序号', width: 'auto', sortable: true },
        { prop: 'zcliServiceLife', label: '土地使用年限(年)', width: 'auto', sortable: true },
        { prop: 'zcliStateCz', label: '出租面积', width: 'auto', sortable: true },
        { prop: 'zcliStateXz', label: '闲置面积', width: 'auto', sortable: true },
        { prop: 'zcliStateZy', label: '自用面积', width: 'auto', sortable: true },
        { prop: 'zcliStrategyDescribe', label: '是否有战略安排', width: 'auto', sortable: true },
        { prop: 'zcliSurroundingRentPrice', label: '上年周边可比土地出租单价（元/平方米/月）', width: 'auto', sortable: true },
        { prop: 'zcliSurroundingSalePrice', label: '上年周边可比土地出售单价（元/平方米/月）', width: 'auto', sortable: true },
        { prop: 'zcliTotalDepreciation', label: '本年计提折旧总额（万元）', width: 'auto', sortable: true },
        { prop: 'zcliType', label: '取得方式', width: 'auto', sortable: true },
        { prop: 'zcliUseDescribe', label: '现状用途描述', width: 'auto', sortable: true },
        { prop: 'zcliUseful', label: '土地性质', width: 'auto', sortable: true },
        { prop: 'zcliYear', label: '年份', width: 'auto', sortable: true },
        { prop: 'zcpiName', label: '批复项目名称', width: 'auto', sortable: true },
        { prop: 'zcxpId', label: '关联项目类固定资产主键ID', width: 'auto', sortable: true }
      ],
      list: [],
      imageList: [],
      listLoading: true,
      layout: 'total, sizes, prev, pager, next, jumper',
      total: 0,
      row: '',
      searchForm: {
        amaiId: '',
        amaiIdEconomize: '',
        amaiIdMarket: '',
        cetcCode: '',
        companyId: '',
        companyName: '',
        companyTid: '',
        companyTname: '',
        networkFlag: '',
        zcliAddress: '',
        zcliArea: '',
        zcliAssetsNo: '',
        zcliAsssetsmType: '',
        zcliBookValue: '',
        zcliBusinessCyjc: '',
        zcliBusinessDzzb: '',
        zcliBusinessOther: '',
        zcliBusinessWlaq: '',
        zcliBusinessWxtx: '',
        zcliCategory: '',
        zcliCertificateCode: '',
        zcliCheckOpinion: '',
        zcliCheckState: '',
        zcliCity: '',
        zcliCommitState: '',
        zcliCompanyLeader: '',
        zcliCompanyTel: '',
        zcliCorrespondingIncome: '',
        zcliCountry: '',
        zcliCounty: '',
        zcliCreditCode: '',
        zcliDate: '',
        zcliDepartmentLeader: '',
        zcliDepartmentTel: '',
        zcliDepreciableYear: '',
        zcliDeptName: '',
        zcliEvaluateDate: '',
        zcliEvaluateValue: '',
        zcliIdleTime: '',
        zcliIfAssets: '',
        zcliIfConstruction: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfExist: '',
        zcliIfIssue: '',
        zcliIfMortgage: '',
        zcliIfStagnation: '',
        zcliLargeCategory: '',
        zcliLatitude: '',
        zcliLongitude: '',
        zcliMethods: '',
        zcliMj: '',
        zcliMortgage: '',
        zcliNetbookValue: '',
        zcliNocertificateReason: '',
        zcliOperator: '',
        zcliOperatorTel: '',
        zcliParkName: '',
        zcliPlotRatio: '',
        zcliProvince: '',
        zcliRange: '',
        zcliReasonDisposal: '',
        zcliReasonIdleness: '',
        zcliReasonStagnation: '',
        zcliRemark: '',
        zcliRentalIncomeLastyear: '',
        zcliRentalIncomeThisyear: '',
        zcliRequirements: '',
        zcliSerialNumber: '',
        zcliServiceLife: '',
        zcliStateCz: '',
        zcliStateXz: '',
        zcliStateZy: '',
        zcliStrategyDescribe: '',
        zcliSurroundingRentPrice: '',
        zcliSurroundingSalePrice: '',
        zcliTotalDepreciation: '',
        zcliType: '',
        zcliUseDescribe: '',
        zcliUseful: '',
        zcliYear: '',
        zcpiName: '',
        zcxpId: '',
        pageNo: 1,
        pageSize: 20,
        sortField: '',
        sortOrder: ''
      },
    }
  },
  computed: {
    ...mapGetters({
      username: 'user/username',
      loginUser: 'user/loginUser'
    }),
    finallyColumns () {
      return this.columns.filter((item) =>
        this.checkList.includes(item.label)
      )
    },
  },
  created () {
    this.fetchData()
  },
  methods: {
    count (index) {
      return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
    },
    //列排序事件
    sortChange (sortColumn) {
      this.searchForm.sortField = sortColumn.prop
      this.searchForm.sortOrder = sortColumn.order
      this.fetchData()
    },
    // 弹窗保存确认按钮
    save () {
      this.$refs.zcglLandInfoForm.$refs.form.validate(async (valid) => {
        if (valid) {
          //系统如记录操作日志，请修改日志信息
          this.form.logData = JSON.stringify(this.form)
          if (this.form.zcliId) {
            this.form.logDesc = "修改数据"
          } else {
            this.form.logDesc = "新增数据"
          }
          const msg = await zcglLandInfoDoSaveOrUpdLog(this.form)
          if (msg.code == 200) {
            this.$message({ message: '保存操作成功!', type: 'success' })
            this.fetchData()
            this.close()
          } else {
            this.$message({ message: '保存操作失败!', type: 'warning' })
          }
        }
      })
    },
    // 弹窗编辑取消按钮
    close () {
      this.$refs.zcglLandInfoEdit.close()
    },
    // 可拖拽列复选框点击事件
    handleCheckedChange ($event) {
      this.checkList = $event
    },
    // 全屏事件
    handleHeight ($event) {
      this.isFullscreen = $event
      if ($event) {
        this.height = this.$baseTableHeight(1, 1) + 150
      } else {
        this.height = this.$baseTableHeight(1, 1)
      }
    },
    // 行点击切换事件
    currentSelectRow (val) {
      this.row = val
    },
    // 添加按钮事件
    handleAdd () {
      this.form = {}
      this.editType = 'add'
      this.$refs['zcglLandInfoEdit'].showEdit('添加')
    },
    // 双击行编辑事件
    cellDblClick (row) {
      this.handleEdit(row)
    },
    // 编辑行数据
    handleEdit (row) {
      this.row = row
      this.editType = 'update'
      this.$refs['zcglLandInfoEdit'].showEdit('编辑')
      this.form = Object.assign({}, row)
    },
    // 删除行数据
    handleDelete (row) {
      if (row.zcliId) {
        this.$baseConfirm('确定删除吗', null, async () => {
          let row1 = {}
          row1.logData = JSON.stringify(row)
          row1.logDesc = '删除固定资产--土地信息表数据'
          row1.zcliId = row.zcliId
          const msg = await zcglLandInfoDoDeleteELog(row1)
          if (msg.code == 200) {
            this.$message({ message: '删除操作成功!', type: 'success' })
            await this.fetchData()
          } else {
            this.$message({ message: '删除操作失败!', type: 'warning' })
          }
        })
      } else {
        if (this.selectRows.length > 0) {
          const ids = this.selectRows.map((item) => item.zcliId).join()
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await zcglLandInfoDoDeleteELog({ "zcliId": ids, "logDesc": "删除固定资产--土地信息表数据" })
            if (msg.code == 200) {
              this.$message({ message: '删除操作成功!', type: 'success' })
              await this.fetchData()
            } else {
              this.$message({ message: '删除操作失败!', type: 'warning' })
            }
          })
        } else {
          this.$baseMessage('请选择要删除的数据', 'error', 'vab-hey-message-error')
        }
      }
    },
    // 分页每页条数改变
    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },
    // 分页当前页改变
    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },
    // 快速查询
    handleSearch ($event) {
      this.searchForm = $event
      this.searchForm.pageNo = 1
      this.fetchData()
    },
    //高级查询弹框
    handleQuery () {
      this.queryForm = Object.assign(this.queryForm, this.searchForm)
      this.$refs['zcglLandInfoQuerySearch'].showQuery('查询')
    },
    //高级查询关闭
    queryClose () {
      this.$refs.zcglLandInfoQuerySearch.close()
    },
    //高级查询清空
    queryClear () {
      this.queryForm = Object.assign(this.queryForm, this.queryFormDf)
    },
    //高级查询
    querySure () {
      for (let key in this.queryForm) {
        this.searchForm[key] = this.queryForm[key]
      }
      this.searchForm.pageNo = 1
      this.$refs.zcglLandInfoQuerySearch.close()
      this.fetchData()
    },
    // 获取表格数据
    async fetchData () {
      this.listLoading = true
      const {
        data: { list, total },
      } = await zcglLandInfoGetList(this.searchForm)
      this.list = list
      this.total = total
      this.listLoading = false
    },
    // 后端导出
    async handleExportRear () {
      let params = {
        "dataFields": { "createdTime": { "celltype": "date" }, "updatedTime": { "celltype": "date" }, "zcliIdleStartTime": { "celltype": "date" } },
        "fileName": "固定资产--土地信息表.xls",
        "isnumber": true,
        "excelTitle": "固定资产--土地信息表",
        "queryForm": this.searchForm || {}
      }
      let qf = exportRearEnd("#ZcglLandInfo", params)
      const { msg } = await zcglLandInfoDoExport(qf)
      window.open(baseURL + "/" + msg)
    },
    //后端导出模板
    async handleExportTmpl () {
      let params = { "fileName": "固定资产--土地信息表模板.xls", "excelIstmpl": true }
      let qf = exportRearEnd("#ZcglLandInfo", params)
      const { msg } = await zcglLandInfoDoExport(qf)
      window.open(baseURL + "/" + msg)
    },
    // excel导入
    handleImportRear () {
      this.fetchData()
    }
  },
}
</script>