import request from '@/utils/request'

//列表分页加载数据
export function zcglLandInfoGetList(params) {
  return request({
    url: '/zcgl-land-info/vPage',
    method: 'get',
    params,
  })
}
//保存新增,不记录日志
export function zcglLandInfoDoSave(data) {
  return request({
    url: '/zcgl-land-info/save',
    method: 'post',
    data,
  })
}
//保存更新,不记录日志
export function zcglLandInfoDoUpdate(data) {
  return request({
    url: '/zcgl-land-info/update',
    method: 'post',
    data,
  })
}
//保存新增或更新,不记录日志
export function zcglLandInfoDoSaveOrUpd(data) {
  return request({
    url: '/zcgl-land-info/saveOrUpd',
    method: 'post',
    data,
  })
}
//保存新增或更新,记录日志,实体类增加logDesc属性
export function zcglLandInfoDoSaveOrUpdLog(data) {
  return request({
    url: '/zcgl-land-info/saveOrUpdLog',
    method: 'post',
    data,
  })
}
//根据主键删除,不记录日志,data.id可为多个id逗号分隔
export function zcglLandInfoDoDelete(data) {
  return request({
    url: '/zcgl-land-info/delete-by-id/'+data.id,
    method: 'delete',
    data,
  })
}
//根据主键删除,记录日志,实体类主键可为多个且逗号分隔，logDesc
export function zcglLandInfoDoDeleteLog(data) {
  return request({
    url: '/zcgl-land-info/delete-by-id-log',
    method: 'post',
    data,
  })
}
//根据条件删除,记录日志,参数为实体，logDesc
export function zcglLandInfoDoDeleteELog(data) {
  return request({
    url: '/zcgl-land-info/deleteLog',
    method: 'post',
    data,
  })
}
//树形表格查询
export function zcglLandInfoGetTreeList(params){
  return request({
    url: '/zcgl-land-info/vTreeList',
    method: 'get',
    params,
  })
}
//后端导出
export function zcglLandInfoDoExport(data) {
  return request({
    url: '/zcgl-land-info/vExport',
    method: 'post',
    data,
  })
}
//统计数据
export function zcglLandInfoGetStat(params) {
  return request({
    url: '/zcgl-land-info/vStat',
    method: 'get',
    params,
  })
}
