<template>
  <div class="Form-container" >
    <el-row :gutter="20">
      <el-form
        ref="form"
        label-width="120px"
        :model="form"
        :rules="rules">
        <el-col :span="8">
          <el-form-item label="坐落位置（区）" prop="amaiId">
            <el-input v-model.trim="form.amaiId" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="坐落位置---省" prop="amaiIdEconomize">
            <el-input v-model.trim="form.amaiIdEconomize" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="坐落位置-市" prop="amaiIdMarket">
            <el-input v-model.trim="form.amaiIdMarket" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="集团编码" prop="cetcCode">
            <el-input v-model.trim="form.cetcCode" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本单位，关联ZCGL_COMPANY_INFO" prop="companyId">
            <el-input v-model.trim="form.companyId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本单位名称" prop="companyName">
            <el-input v-model.trim="form.companyName" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="二级成员单位" prop="companyTid">
            <el-input v-model.trim="form.companyTid" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="二级单位名称" prop="companyTname">
            <el-input v-model.trim="form.companyTname" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据来源" prop="networkFlag">
            <el-input v-model.trim="form.networkFlag" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="具体土地位置" prop="zcliAddress">
            <el-input v-model.trim="form.zcliAddress" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地总面积" prop="zcliArea">
            <el-input-number v-model.trim="form.zcliArea" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资产编号" prop="zcliAssetsNo">
            <el-input v-model.trim="form.zcliAssetsNo" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资产类型（事业单位资产、企业单位资产）" prop="zcliAsssetsmType">
            <el-input v-model.trim="form.zcliAsssetsmType" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="原值" prop="zcliBookValue">
            <el-input-number v-model.trim="form.zcliBookValue" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产业基础面积" prop="zcliBusinessCyjc">
            <el-input-number v-model.trim="form.zcliBusinessCyjc" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电子装备面积" prop="zcliBusinessDzzb">
            <el-input-number v-model.trim="form.zcliBusinessDzzb" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="其它面积" prop="zcliBusinessOther">
            <el-input-number v-model.trim="form.zcliBusinessOther" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="网络安全面积" prop="zcliBusinessWlaq">
            <el-input-number v-model.trim="form.zcliBusinessWlaq" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="网信体系面积" prop="zcliBusinessWxtx">
            <el-input-number v-model.trim="form.zcliBusinessWxtx" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="门类" prop="zcliCategory">
            <el-input v-model.trim="form.zcliCategory" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地权属证明编号" prop="zcliCertificateCode">
            <el-input v-model.trim="form.zcliCertificateCode" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核意见" prop="zcliCheckOpinion">
            <el-input v-model.trim="form.zcliCheckOpinion" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核状态" prop="zcliCheckState">
            <el-input v-model.trim="form.zcliCheckState" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地所在市" prop="zcliCity">
            <el-input v-model.trim="form.zcliCity" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提交状态" prop="zcliCommitState">
            <el-input v-model.trim="form.zcliCommitState" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分管所(公司)领导" prop="zcliCompanyLeader">
            <el-input v-model.trim="form.zcliCompanyLeader" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分管所(公司)领导联系方式" prop="zcliCompanyTel">
            <el-input v-model.trim="form.zcliCompanyTel" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地面积对应本年营业收入（万元）" prop="zcliCorrespondingIncome">
            <el-input-number v-model.trim="form.zcliCorrespondingIncome" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="国家或地区" prop="zcliCountry">
            <el-input v-model.trim="form.zcliCountry" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地所在区县" prop="zcliCounty">
            <el-input v-model.trim="form.zcliCounty" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="统一社会信用代码" prop="zcliCreditCode">
            <el-input v-model.trim="form.zcliCreditCode" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="登记时间" prop="zcliDate">
            <el-input v-model.trim="form.zcliDate" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门负责人" prop="zcliDepartmentLeader">
            <el-input v-model.trim="form.zcliDepartmentLeader" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门负责人联系方式" prop="zcliDepartmentTel">
            <el-input v-model.trim="form.zcliDepartmentTel" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="折旧年限" prop="zcliDepreciableYear">
            <el-input v-model.trim="form.zcliDepreciableYear" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务主管部门名称" prop="zcliDeptName">
            <el-input v-model.trim="form.zcliDeptName" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最近评估日期" prop="zcliEvaluateDate">
            <el-input v-model.trim="form.zcliEvaluateDate" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最近评估价值（万元）" prop="zcliEvaluateValue">
            <el-input-number v-model.trim="form.zcliEvaluateValue" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="闲置起始时间" prop="zcliIdleStartTime">
            <el-date-picker v-model.trim="form.zcliIdleStartTime" style="width:100%" type="date" clearable value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="空置时间" prop="zcliIdleTime">
            <el-input-number v-model.trim="form.zcliIdleTime" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否两非资产" prop="zcliIfAssets">
            <el-input v-model.trim="form.zcliIfAssets" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否在建" prop="zcliIfConstruction">
            <el-input v-model.trim="form.zcliIfConstruction" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否可处置" prop="zcliIfDispose">
            <el-input v-model.trim="form.zcliIfDispose" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否存在纠纷" prop="zcliIfDispute">
            <el-input v-model.trim="form.zcliIfDispute" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否取得土地权属证明编号" prop="zcliIfExist">
            <el-input v-model.trim="form.zcliIfExist" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否下达" prop="zcliIfIssue">
            <el-input v-model.trim="form.zcliIfIssue" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地是否已抵押" prop="zcliIfMortgage">
            <el-input v-model.trim="form.zcliIfMortgage" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否延期或停滞" prop="zcliIfStagnation">
            <el-input v-model.trim="form.zcliIfStagnation" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="大类" prop="zcliLargeCategory">
            <el-input v-model.trim="form.zcliLargeCategory" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="纬度" prop="zcliLatitude">
            <el-input v-model.trim="form.zcliLatitude" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经度" prop="zcliLongitude">
            <el-input v-model.trim="form.zcliLongitude" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建议盘活/处置方式" prop="zcliMethods">
            <el-input v-model.trim="form.zcliMethods" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="密级" prop="zcliMj">
            <el-input v-model.trim="form.zcliMj" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="其中：已抵押面积" prop="zcliMortgage">
            <el-input-number v-model.trim="form.zcliMortgage" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="净值" prop="zcliNetbookValue">
            <el-input-number v-model.trim="form.zcliNetbookValue" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="其中：未取得土地证明原因" prop="zcliNocertificateReason">
            <el-input v-model.trim="form.zcliNocertificateReason" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人" prop="zcliOperator">
            <el-input v-model.trim="form.zcliOperator" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="经办人联系方式" prop="zcliOperatorTel">
            <el-input v-model.trim="form.zcliOperatorTel" maxlength="50" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本地块所属园区" prop="zcliParkName">
            <el-input v-model.trim="form.zcliParkName" maxlength="500" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="容积率" prop="zcliPlotRatio">
            <el-input v-model.trim="form.zcliPlotRatio" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地所在省（直辖市、自治区）" prop="zcliProvince">
            <el-input v-model.trim="form.zcliProvince" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="境内境外" prop="zcliRange">
            <el-input v-model.trim="form.zcliRange" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="不可处置原因" prop="zcliReasonDisposal">
            <el-input v-model.trim="form.zcliReasonDisposal" maxlength="500" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="闲置原因" prop="zcliReasonIdleness">
            <el-input v-model.trim="form.zcliReasonIdleness" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="延期/停滞原因" prop="zcliReasonStagnation">
            <el-input v-model.trim="form.zcliReasonStagnation" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="zcliRemark">
            <el-input v-model.trim="form.zcliRemark" maxlength="2000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上年租金收入（万元）" prop="zcliRentalIncomeLastyear">
            <el-input-number v-model.trim="form.zcliRentalIncomeLastyear" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预计本年租金收入（万元）" prop="zcliRentalIncomeThisyear">
            <el-input-number v-model.trim="form.zcliRentalIncomeThisyear" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地上建筑是否满族批复或者备案功能要求" prop="zcliRequirements">
            <el-input v-model.trim="form.zcliRequirements" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="序号" prop="zcliSerialNumber">
            <el-input-number v-model.trim="form.zcliSerialNumber" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地使用年限(年)" prop="zcliServiceLife">
            <el-input v-model.trim="form.zcliServiceLife" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="出租面积" prop="zcliStateCz">
            <el-input-number v-model.trim="form.zcliStateCz" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="闲置面积" prop="zcliStateXz">
            <el-input-number v-model.trim="form.zcliStateXz" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="自用面积" prop="zcliStateZy">
            <el-input-number v-model.trim="form.zcliStateZy" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否有战略安排" prop="zcliStrategyDescribe">
            <el-input v-model.trim="form.zcliStrategyDescribe" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上年周边可比土地出租单价（元/平方米/月）" prop="zcliSurroundingRentPrice">
            <el-input-number v-model.trim="form.zcliSurroundingRentPrice" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上年周边可比土地出售单价（元/平方米/月）" prop="zcliSurroundingSalePrice">
            <el-input-number v-model.trim="form.zcliSurroundingSalePrice" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本年计提折旧总额（万元）" prop="zcliTotalDepreciation">
            <el-input-number v-model.trim="form.zcliTotalDepreciation" style="width:100%" :precision="0" :min="0" :max="99999999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="取得方式" prop="zcliType">
            <el-input v-model.trim="form.zcliType" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="现状用途描述" prop="zcliUseDescribe">
            <el-input v-model.trim="form.zcliUseDescribe" maxlength="1000" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="土地性质" prop="zcliUseful">
            <el-input v-model.trim="form.zcliUseful" maxlength="200" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年份" prop="zcliYear">
            <el-input v-model.trim="form.zcliYear" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="批复项目名称" prop="zcpiName">
            <el-input v-model.trim="form.zcpiName" maxlength="100" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="关联项目类固定资产主键ID" prop="zcxpId">
            <el-input v-model.trim="form.zcxpId" maxlength="20" show-word-limit clearable ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="ID" prop="zcliId"  style="display:none;">
            <el-input v-model.trim="form.zcliId" type="hidden"></el-input>
          </el-form-item>
        </el-col>

      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getSysValList } from '@/api/lesysparamvals'
  export default {
    name: 'zcglLandInfoForm',
    props: {
      rules: {
        type: Object,
        require: true
      },
      form: {
        type: Object,
        require: true
      },
      type: {
        type: String,
        require: true,
      },
      formConfig: {
        type: Object,
        require: true
      }
    },
    data() {
      return {
        tableForm: this.form,
        tableRules: this.rules,
        labelWidth: this.formConfig.labelWidth,
        size: this.formConfig.size,
        labelPosition: this.formConfig.labelPosition,
        //
        optionsData:{
          
        }
      }
    },
    created() {
      // 获取下拉框数据
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvId,label:data[d].lpvName})
          }
        }
      }
    },
    watch: {
      form(newVal) {
        this.tableForm = newVal
      }
    }
  }
</script>